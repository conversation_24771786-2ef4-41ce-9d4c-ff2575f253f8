<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        .success {
            color: #4CAF50;
            font-size: 24px;
            margin: 20px 0;
        }
        .info {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍽️ 楠楠家厨后台管理系统</h1>
        <div class="success">✅ Vite服务器正常工作！</div>
        <div class="info">当前时间: <span id="time"></span></div>
        <div class="info">服务器地址: http://localhost:3001</div>
        <div class="info">如果你能看到这个页面，说明服务器运行正常</div>
        
        <script>
            function updateTime() {
                document.getElementById('time').textContent = new Date().toLocaleString();
            }
            updateTime();
            setInterval(updateTime, 1000);
            
            console.log('🎉 静态测试页面加载成功！');
            console.log('Vite服务器正在正常工作');
        </script>
    </div>
</body>
</html>
