<template>
  <div class="user-management">
    <CustomTable
      title="用户管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出用户
        </el-button>
      </template>
      
      <template #user="{ row }">
        <div class="user-info">
          <el-avatar :src="row.avatar" :size="40">
            {{ row.name.charAt(0) }}
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ row.name }}</div>
            <div class="user-phone">{{ row.phone }}</div>
          </div>
        </div>
      </template>
      
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="1"
          :inactive-value="0"
          active-text="正常"
          inactive-text="禁用"
          @change="handleStatusChange(row)"
        />
      </template>
      
      <template #orderCount="{ row }">
        <el-tag type="primary">{{ row.orderCount }}单</el-tag>
      </template>
      
      <template #totalAmount="{ row }">
        <span class="amount-text">¥{{ row.totalAmount }}</span>
      </template>
      
      <template #lastLoginTime="{ row }">
        <span>{{ formatTime(row.lastLoginTime) }}</span>
      </template>
      
      <template #operation="{ row }">
        <el-button size="small" @click="handleViewUser(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEditUser(row)">编辑</el-button>
        <el-button 
          size="small" 
          :type="row.status === 1 ? 'warning' : 'success'" 
          @click="handleToggleStatus(row)"
        >
          {{ row.status === 1 ? '禁用' : '启用' }}
        </el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { userApi } from '@/api/user'
import { formatTime } from '@/utils/common'
import dayjs from 'dayjs'

const loading = ref(false)
const tableData = ref([])

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  name: '',
  phone: '',
  status: ''
})

// 表格列配置
const columns = [
  { prop: 'user', label: '用户信息', width: 200, slot: true },
  { prop: 'orderCount', label: '订单数量', width: 120, slot: true },
  { prop: 'totalAmount', label: '消费总额', width: 120, slot: true },
  { prop: 'lastLoginTime', label: '最后登录', width: 160, slot: true },
  { prop: 'registerTime', label: '注册时间', width: 160, formatter: (row) => formatTime(row.registerTime) },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { label: '操作', width: 200, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'name', label: '用户姓名', type: 'input' },
  { prop: 'phone', label: '手机号', type: 'input' },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '正常', value: '1' },
    { label: '禁用', value: '0' }
  ]}
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据
    tableData.value = generateMockData()
    pagination.total = 50
  } catch (error) {
    console.error('加载用户数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const data = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  
  for (let i = 0; i < 10; i++) {
    const orderCount = Math.floor(Math.random() * 30) + 5
    const totalAmount = Math.floor(Math.random() * 2000) + 500
    
    data.push({
      id: i + 1,
      name: names[i % names.length],
      phone: `138****800${i + 1}`,
      avatar: `https://picsum.photos/100/100?random=${i + 1}`,
      orderCount,
      totalAmount: totalAmount.toFixed(2),
      lastLoginTime: dayjs().subtract(Math.floor(Math.random() * 7), 'day').toDate(),
      registerTime: dayjs().subtract(Math.floor(Math.random() * 365), 'day').toDate(),
      status: Math.random() > 0.2 ? 1 : 0
    })
  }
  return data
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleViewUser = (row) => {
  ElMessage.info(`查看用户：${row.name}`)
}

const handleEditUser = (row) => {
  ElMessage.info(`编辑用户：${row.name}`)
}

const handleStatusChange = async (row) => {
  try {
    ElMessage.success('用户状态更新成功')
  } catch (error) {
    console.error('更新用户状态失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${row.name} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    row.status = row.status === 1 ? 0 : 1
    ElMessage.success(`用户${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`用户${action}失败`)
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.user-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

.user-info {
  @apply flex items-center space-x-3;
  
  .user-details {
    .user-name {
      @apply text-sm font-medium text-gray-900;
    }
    
    .user-phone {
      @apply text-xs text-gray-500;
    }
  }
}

.amount-text {
  @apply text-green-600 font-semibold;
}
</style>
