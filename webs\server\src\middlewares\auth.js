const { verifyToken } = require('../utils/jwt');
const { error } = require('../utils/response');

/**
 * 认证中间件
 * 验证请求头中的 JWT token
 */
const auth = (req, res, next) => {
  try {
    // 获取 Authorization 头
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, 'Authentication required', 401);
    }
    
    // 提取 token
    const token = authHeader.split(' ')[1];
    
    // 验证 token
    const decoded = verifyToken(token);
    
    if (!decoded) {
      return error(res, 'Invalid or expired token', 401);
    }
    
    // 将用户信息添加到请求对象
    req.user = decoded;
    
    next();
  } catch (err) {
    return error(res, 'Authentication failed', 401);
  }
};

/**
 * 管理员权限中间件
 * 验证用户是否具有管理员权限
 */
const adminAuth = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return error(res, 'Admin privileges required', 403);
  }
  
  next();
};

module.exports = {
  auth,
  adminAuth
};
