import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia'
import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { useUserStore } from '@/stores/user'

// Mock API
jest.mock('@/api/auth', () => ({
  login: jest.fn()
}))

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    localStorage.clear()
  })

  it('initializes with empty state', () => {
    const store = useUserStore()
    
    expect(store.token).toBe('')
    expect(store.userInfo).toBeNull()
    expect(store.isLoggedIn).toBe(false)
  })

  it('loads token from localStorage', () => {
    localStorage.setItem('admin_token', 'test-token')
    
    const store = useUserStore()
    
    expect(store.token).toBe('test-token')
    expect(store.isLoggedIn).toBe(true)
  })

  it('handles successful login', async () => {
    const { login } = require('@/api/auth')
    login.mockResolvedValue({
      code: 200,
      data: {
        token: 'new-token',
        user: { id: 1, name: '测试用户' }
      }
    })

    const store = useUserStore()
    const result = await store.login({
      username: 'test',
      password: 'password'
    })

    expect(result.success).toBe(true)
    expect(store.token).toBe('new-token')
    expect(store.userInfo).toEqual({ id: 1, name: '测试用户' })
    expect(localStorage.setItem).toHaveBeenCalledWith('admin_token', 'new-token')
  })

  it('handles failed login', async () => {
    const { login } = require('@/api/auth')
    login.mockResolvedValue({
      code: 400,
      message: '用户名或密码错误'
    })

    const store = useUserStore()
    const result = await store.login({
      username: 'test',
      password: 'wrong'
    })

    expect(result.success).toBe(false)
    expect(result.message).toBe('用户名或密码错误')
    expect(store.token).toBe('')
  })

  it('clears data on logout', () => {
    const store = useUserStore()
    store.token = 'test-token'
    store.userInfo = { id: 1, name: '测试用户' }

    store.logout()

    expect(store.token).toBe('')
    expect(store.userInfo).toBeNull()
    expect(localStorage.removeItem).toHaveBeenCalledWith('admin_token')
    expect(localStorage.removeItem).toHaveBeenCalledWith('admin_user')
  })

  it('initializes user info from localStorage', () => {
    const userInfo = { id: 1, name: '测试用户' }
    localStorage.getItem.mockReturnValue(JSON.stringify(userInfo))

    const store = useUserStore()
    store.initUserInfo()

    expect(store.userInfo).toEqual(userInfo)
  })

  it('handles invalid user info in localStorage', () => {
    localStorage.getItem.mockReturnValue('invalid-json')

    const store = useUserStore()
    store.initUserInfo()

    expect(store.userInfo).toBeNull()
    expect(store.token).toBe('')
  })
})
