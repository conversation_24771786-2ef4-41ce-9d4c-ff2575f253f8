const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { auth, adminAuth } = require('../middlewares/auth');

// 获取通知列表
router.get('/', notificationController.getNotifications);

// 获取指定通知
router.get('/:id', notificationController.getNotificationById);

// 创建通知 (需要管理员权限)
router.post('/', auth, adminAuth, notificationController.createNotification);

// 更新通知 (需要认证)
router.put('/:id', auth, notificationController.updateNotification);

// 删除通知 (需要认证)
router.delete('/:id', auth, notificationController.deleteNotification);

module.exports = router;
