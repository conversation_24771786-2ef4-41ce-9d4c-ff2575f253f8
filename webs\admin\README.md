```agsl
├── .env                  # 基础环境变量配置文件（优先级最低）
├── .env.development      # 开发环境变量配置文件
├── .env.production       # 生产环境变量配置文件
├── .env.staging          # 预发布环境变量配置文件
```

```agsl
{
  "Version": "3.9.6", // 平台版本号
  "Title": "Hl", // 平台标题
  "FixedHeader": true, // 是否固定页头和标签页（true 内容区超出出现纵向滚动条 false 页头、标签页、内容区可纵向滚动）
  "HiddenSideBar": false, // 隐藏菜单和页头，只显示标签页和内容区
  "MultiTagsCache": false, // 是否开启持久化标签 （会缓存）
  "KeepAlive": true, // 是否开启组件缓存（此处不同于路由的 keepAlive，如果此处为 true 表示设置路由的 keepAlive 起效，反之设置 false 屏蔽平台整体的 keepAlive，即使路由设置了keepAlive 也不再起作用）
  "Locale": "zh", // 默认国际化语言 （zh 中文、en 英文）（会缓存）
  "Layout": "vertical", // 导航菜单模式 （vertical 左侧菜单模式、horizontal 顶部菜单模式、mix 综合菜单模式）（会缓存）
  "Theme": "default", // 主题模式（会缓存）
  "DarkMode": false, // 是否开启暗黑模式 （会缓存）
  "Grey": false, // 灰色模式（会缓存）
  "Weak": false, // 色弱模式（会缓存）
  "HideTabs": false, // 是否隐藏标签页（会缓存）
  "SidebarStatus": true, // vertical左侧菜单模式模式下侧边栏状态（true 展开、false 收起）（会缓存）
  "EpThemeColor": "#409EFF", // 主题色（会缓存）
  "ShowLogo": true, // 是否显示logo（会缓存）
  "ShowModel": "smart", // 标签页风格（smart 灵动模式、card 卡片模式）（会缓存）
  "MenuArrowIconNoTransition": false, // 菜单展开、收起图标是否开启动画，如遇菜单展开、收起卡顿设置成 true 即可（默认 false，开启动画）
  "CachingAsyncRoutes": true, // 是否开启动态路由缓存本地的全局配置，默认 true
  "TooltipEffect": "light" // 可配置平台主体所有 el-tooltip 的 effect 属性，默认 light，不会影响业务代码
}
```