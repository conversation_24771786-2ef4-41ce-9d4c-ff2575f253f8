<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>楠楠家厨管理系统</title>
    <link rel="icon" href="/favicon.ico" />
  </head>
  <body>
    <div id="app">
      <style>
        html,
        body,
        #app {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          margin: 0;
          padding: 0;
          overflow: hidden;
          font-family:
            "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
            "Microsoft YaHei", Arial, sans-serif;
        }

        .loader {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #409eff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        .loading-text {
          margin-top: 20px;
          color: #666;
          font-size: 14px;
        }
      </style>
      <div>
        <div class="loader"></div>
        <div class="loading-text">正在加载楠楠家厨管理系统...</div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
