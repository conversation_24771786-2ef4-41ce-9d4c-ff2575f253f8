Page({
  data: {
    weeklyFavorite: '红烧肉（8次）',
    monthlyTotal: 32,
    healthTip: '本月荤菜偏多，建议多点蔬菜哦！',
    hotRank: [
      {
        name: '红烧肉',
        count: 18,
        img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg'
      },
      {
        name: '宫保鸡丁',
        count: 14,
        img: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg'
      },
      {
        name: '清炒时蔬',
        count: 12,
        img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/salad-1238248_1280.jpg'
      }
    ]
  },

  onLoad() {
    // 这里可以添加从服务器获取统计数据的逻辑
    this.getStatisticsData();
  },

  // 获取统计数据
  getStatisticsData() {
    // 模拟从服务器获取数据
    // 实际项目中，这里应该是一个网络请求
    // 如果需要，可以在这里更新页面数据
    // this.setData({
    //   weeklyFavorite: '获取的数据',
    //   monthlyTotal: 获取的数据,
    //   healthTip: '获取的数据',
    //   hotRank: 获取的数据
    // });
  }
});
