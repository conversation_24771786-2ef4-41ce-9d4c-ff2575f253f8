{"appid": "wx82283b353918af82", "compileType": "miniprogram", "libVersion": "3.4.3", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "useCompilerPlugins": ["sass"], "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": ""}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "projectname": "nannan"}