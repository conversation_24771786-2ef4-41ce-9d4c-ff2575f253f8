<template>
  <div class="message-list">
    <CustomTable
      title="家庭留言"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @current-change="handleCurrentChange"
    >
      <template #read="{ row }">
        <el-tag :type="row.read ? 'success' : 'warning'">
          {{ row.read ? '已读' : '未读' }}
        </el-tag>
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" @click="handleMarkRead(row)" v-if="!row.read">标记已读</el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomTable from '@/components/CustomTable.vue'
import { messageApi } from '@/api/message'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'MessageList',
  components: {
    CustomTable
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'userName', label: '用户' },
      { prop: 'content', label: '内容', showOverflowTooltip: true },
      { prop: 'read', label: '状态', slot: 'read' },
      { prop: 'createdAt', label: '时间', formatter: (row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm') }
    ]
    
    const loadData = async () => {
      loading.value = true
      try {
        const response = await messageApi.getMessages({
          page: pagination.page,
          size: pagination.size
        })
        if (response.data) {
          tableData.value = response.data.list || response.data
          pagination.total = response.data.total || response.data.length
        }
      } catch (error) {
        console.error('加载留言失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }
    
    const handleMarkRead = async (row) => {
      try {
        await messageApi.markAsRead(row.id)
        ElMessage.success('标记已读成功')
        loadData()
      } catch (error) {
        console.error('标记已读失败:', error)
        ElMessage.error('操作失败')
      }
    }
    
    const handleDelete = async (row) => {
      try {
        await messageApi.deleteMessage(row.id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error) {
        console.error('删除留言失败:', error)
        ElMessage.error('删除失败')
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      tableData,
      columns,
      pagination,
      handleCurrentChange,
      handleMarkRead,
      handleDelete
    }
  }
})
</script>
