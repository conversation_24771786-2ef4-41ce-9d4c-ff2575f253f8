const app = getApp();

Page({
  data: {
    notices: [
      {
        id: 1,
        content: '今晚8点厨房消毒，请提前取餐！',
        time: '2025-04-29 10:00'
      },
      {
        id: 2,
        content: '本周六有家庭聚餐，欢迎大家提前点菜！',
        time: '2025-04-28 09:00'
      }
    ],
    noticeInput: '',
    userInfo: {}
  },

  onLoad() {
    // 获取用户信息
    if (app.globalData && app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }
  },

  // 通知输入
  onNoticeInput(e) {
    this.setData({
      noticeInput: e.detail.value
    });
  },

  // 添加通知
  addNotice() {
    const {noticeInput, notices} = this.data;

    if (!noticeInput.trim()) {
      return;
    }

    // 获取当前时间
    const now = new Date();
    const time = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
      2,
      '0'
    )}-${String(now.getDate()).padStart(2, '0')} ${String(
      now.getHours()
    ).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    // 创建新通知
    const newNotice = {
      id: notices.length + 1,
      content: noticeInput,
      time
    };

    // 更新通知列表（新通知放在最前面）
    this.setData({
      notices: [newNotice, ...notices],
      noticeInput: ''
    });

    // 提示用户
    wx.showToast({
      title: '通知已发布',
      icon: 'success',
      duration: 1500
    });
  },

  // 跳转到家庭留言页面
  goToFamilyMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index'
    });
  }
});
