const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const {auth, adminAuth} = require('../middlewares/auth');

// 获取订单列表 (需要认证)
router.get('/', auth, orderController.getOrders);

// 获取今日订单 (公开访问，用于展示)
router.get('/today', orderController.getTodayOrders);

// 获取指定订单 (需要认证)
router.get('/:id', auth, orderController.getOrderById);

// 创建订单 (需要认证)
router.post('/', auth, orderController.createOrder);

// 更新订单 (需要认证)
router.put('/:id', auth, orderController.updateOrder);

// 删除订单 (需要认证)
router.delete('/:id', auth, orderController.deleteOrder);

module.exports = router;
