// 路由功能完整性测试
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import { ElMessage } from 'element-plus'

// 导入路由配置
import routes from '@/router/index.js'

// 导入所有页面组件
import Dashboard from '@/views/dashboard/index.vue'
import MenuDishes from '@/views/menu/dishes.vue'
import MenuCategories from '@/views/menu/categories.vue'
import MenuToday from '@/views/menu/today.vue'
import MenuHistory from '@/views/menu/history.vue'
import OrderList from '@/views/order/index.vue'
import OrderToday from '@/views/order/today.vue'
import OrderStatistics from '@/views/order/statistics.vue'
import UserList from '@/views/user/index.vue'
import MessageFamily from '@/views/message/family.vue'
import MessageNotifications from '@/views/message/notifications.vue'
import AnalyticsOverview from '@/views/analytics/overview.vue'
import AnalyticsDishes from '@/views/analytics/dishes.vue'
import AnalyticsUsers from '@/views/analytics/users.vue'
import NotFound from '@/views/404.vue'

// Mock API调用
vi.mock('@/api/menu', () => ({
  dishApi: {
    getDishes: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    getCategories: vi.fn().mockResolvedValue({ data: [] }),
    getDishStatistics: vi.fn().mockResolvedValue({ data: {} })
  },
  menuApi: {
    getMenus: vi.fn().mockResolvedValue({ data: [] }),
    getTodayMenu: vi.fn().mockResolvedValue({ data: {} })
  }
}))

vi.mock('@/api/order', () => ({
  orderApi: {
    getOrders: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    getTodayOrders: vi.fn().mockResolvedValue({ data: [] }),
    getOrderStatistics: vi.fn().mockResolvedValue({ data: {} })
  }
}))

vi.mock('@/api/user', () => ({
  userApi: {
    getUsers: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    getUserStatistics: vi.fn().mockResolvedValue({ data: {} })
  }
}))

vi.mock('@/api/message', () => ({
  messageApi: {
    getFamilyMessages: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    getNotifications: vi.fn().mockResolvedValue({ data: [], total: 0 })
  }
}))

// Mock Element Plus组件
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('路由功能完整性测试', () => {
  let router
  let pinia

  beforeEach(() => {
    // 创建路由实例
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', redirect: '/dashboard' },
        { path: '/dashboard', component: Dashboard },
        { path: '/menu/dishes', component: MenuDishes },
        { path: '/menu/categories', component: MenuCategories },
        { path: '/menu/today', component: MenuToday },
        { path: '/menu/history', component: MenuHistory },
        { path: '/order/list', component: OrderList },
        { path: '/order/today', component: OrderToday },
        { path: '/order/statistics', component: OrderStatistics },
        { path: '/user/list', component: UserList },
        { path: '/message/family', component: MessageFamily },
        { path: '/message/notifications', component: MessageNotifications },
        { path: '/analytics/overview', component: AnalyticsOverview },
        { path: '/analytics/dishes', component: AnalyticsDishes },
        { path: '/analytics/users', component: AnalyticsUsers },
        { path: '/:pathMatch(.*)*', component: NotFound }
      ]
    })

    // 创建Pinia实例
    pinia = createPinia()

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(() => 'mock-token'),
        setItem: vi.fn(),
        removeItem: vi.fn()
      }
    })
  })

  // 测试路由可访问性
  describe('路由可访问性测试', () => {
    const testRoutes = [
      { path: '/dashboard', name: '仪表盘' },
      { path: '/menu/dishes', name: '菜品管理' },
      { path: '/menu/categories', name: '分类管理' },
      { path: '/menu/today', name: '今日菜单' },
      { path: '/menu/history', name: '历史菜单' },
      { path: '/order/list', name: '订单列表' },
      { path: '/order/today', name: '今日订单' },
      { path: '/order/statistics', name: '订单统计' },
      { path: '/user/list', name: '用户管理' },
      { path: '/message/family', name: '家庭留言' },
      { path: '/message/notifications', name: '系统通知' },
      { path: '/analytics/overview', name: '数据概览' },
      { path: '/analytics/dishes', name: '菜品分析' },
      { path: '/analytics/users', name: '用户分析' }
    ]

    testRoutes.forEach(route => {
      it(`应该能够访问 ${route.name} (${route.path})`, async () => {
        await router.push(route.path)
        expect(router.currentRoute.value.path).toBe(route.path)
      })
    })
  })

  // 测试组件挂载
  describe('组件挂载测试', () => {
    it('仪表盘组件应该正常挂载', () => {
      const wrapper = mount(Dashboard, {
        global: {
          plugins: [router, pinia]
        }
      })
      expect(wrapper.exists()).toBe(true)
    })

    it('菜品管理组件应该正常挂载', () => {
      const wrapper = mount(MenuDishes, {
        global: {
          plugins: [router, pinia]
        }
      })
      expect(wrapper.exists()).toBe(true)
    })

    it('订单列表组件应该正常挂载', () => {
      const wrapper = mount(OrderList, {
        global: {
          plugins: [router, pinia]
        }
      })
      expect(wrapper.exists()).toBe(true)
    })

    it('用户管理组件应该正常挂载', () => {
      const wrapper = mount(UserList, {
        global: {
          plugins: [router, pinia]
        }
      })
      expect(wrapper.exists()).toBe(true)
    })

    it('404页面组件应该正常挂载', () => {
      const wrapper = mount(NotFound, {
        global: {
          plugins: [router, pinia]
        }
      })
      expect(wrapper.exists()).toBe(true)
    })
  })

  // 测试404路由
  describe('404路由测试', () => {
    const invalidRoutes = [
      '/list',
      '/dishes',
      '/orders',
      '/users',
      '/messages',
      '/nonexistent'
    ]

    invalidRoutes.forEach(route => {
      it(`访问无效路由 ${route} 应该显示404页面`, async () => {
        await router.push(route)
        expect(router.currentRoute.value.matched[0].components.default).toBe(NotFound)
      })
    })
  })

  // 测试重定向
  describe('重定向测试', () => {
    it('根路径应该重定向到仪表盘', async () => {
      await router.push('/')
      expect(router.currentRoute.value.path).toBe('/dashboard')
    })
  })
})
