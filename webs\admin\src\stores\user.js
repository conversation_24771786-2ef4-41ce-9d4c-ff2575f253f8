import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import {userApi} from '@/api/user';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('admin_token') || '');
  const userInfo = ref(null);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const userName = computed(() => userInfo.value?.name || '');
  const userRole = computed(() => userInfo.value?.role || 'user');

  // 登录
  const login = async credentials => {
    try {
      // 模拟登录API调用
      const mockResponse = {
        success: true,
        data: {
          token: 'mock-admin-token-' + Date.now(),
          user: {
            id: 1,
            name:
              credentials.username === '13800138000' ? '管理员' : '普通用户',
            username: credentials.username,
            role: credentials.username === '13800138000' ? 'admin' : 'user',
            avatar: 'https://picsum.photos/100/100?random=1',
            phone: credentials.username,
            email: '<EMAIL>',
            permissions:
              credentials.username === '13800138000' ? ['*'] : ['read']
          }
        }
      };

      if (mockResponse.success) {
        token.value = mockResponse.data.token;
        userInfo.value = mockResponse.data.user;

        // 保存到本地存储
        localStorage.setItem('admin_token', token.value);
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value));

        return {success: true};
      } else {
        return {success: false, message: mockResponse.message};
      }
    } catch (error) {
      console.error('登录失败:', error);
      return {success: false, message: '登录失败，请重试'};
    }
  };

  // 退出登录
  const logout = () => {
    token.value = '';
    userInfo.value = null;

    // 清除本地存储
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  };

  // 初始化用户信息
  const initUserInfo = () => {
    const userStr = localStorage.getItem('admin_user');
    if (userStr) {
      try {
        userInfo.value = JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        logout();
      }
    }
  };

  // 初始化
  initUserInfo();

  return {
    // 状态
    token,
    userInfo,

    // 计算属性
    isLoggedIn,
    userName,
    userRole,

    // 方法
    login,
    logout,
    initUserInfo
  };
});
