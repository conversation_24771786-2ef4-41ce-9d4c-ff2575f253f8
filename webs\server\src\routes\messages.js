const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');
const { auth } = require('../middlewares/auth');

// 获取消息列表
router.get('/', messageController.getMessages);

// 获取指定消息
router.get('/:id', messageController.getMessageById);

// 创建消息 (需要认证)
router.post('/', auth, messageController.createMessage);

// 更新消息 (需要认证)
router.put('/:id', auth, messageController.updateMessage);

// 删除消息 (需要认证)
router.delete('/:id', auth, messageController.deleteMessage);

module.exports = router;
