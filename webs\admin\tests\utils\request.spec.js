import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import axios from 'axios'
import { http } from '@/utils/request'

// Mock axios
jest.mock('axios')
const mockedAxios = axios

// Mock Element Plus
jest.mock('element-plus', () => ({
  ElMessage: {
    error: jest.fn(),
    success: jest.fn()
  }
}))

// Mock user store
jest.mock('@/stores/user', () => ({
  useUserStore: () => ({
    token: 'test-token',
    logout: jest.fn()
  })
}))

describe('Request Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('http.get', () => {
    it('makes GET request with params', async () => {
      const mockResponse = {
        data: { code: 200, data: { id: 1, name: 'test' } }
      }
      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const result = await http.get('/api/test', { page: 1 })
      
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('http.post', () => {
    it('makes POST request with data', async () => {
      const mockResponse = {
        data: { code: 200, data: { id: 1 } }
      }
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const result = await http.post('/api/test', { name: 'test' })
      
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('http.put', () => {
    it('makes PUT request with data', async () => {
      const mockResponse = {
        data: { code: 200, data: { id: 1, name: 'updated' } }
      }
      mockedAxios.create.mockReturnValue({
        put: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const result = await http.put('/api/test/1', { name: 'updated' })
      
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('http.delete', () => {
    it('makes DELETE request', async () => {
      const mockResponse = {
        data: { code: 200, message: 'deleted' }
      }
      mockedAxios.create.mockReturnValue({
        delete: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const result = await http.delete('/api/test/1')
      
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('http.upload', () => {
    it('makes POST request with FormData', async () => {
      const mockResponse = {
        data: { code: 200, data: { url: 'http://example.com/file.jpg' } }
      }
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const formData = new FormData()
      formData.append('file', new File([''], 'test.jpg'))
      
      const result = await http.upload('/api/upload', formData)
      
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('http.download', () => {
    it('makes GET request with blob response type', async () => {
      const mockResponse = {
        data: new Blob(['file content']),
        config: { responseType: 'blob' }
      }
      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      })

      const result = await http.download('/api/download', { id: 1 })
      
      expect(result).toEqual(mockResponse)
    })
  })
})
