
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>楠楠家厨管理系统 - 路由功能测试报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card.success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
        .summary-card.error { background: linear-gradient(135deg, #f44336 0%, #da190b 100%); }
        .summary-card.warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
        .test-section { margin-bottom: 30px; }
        .test-item { display: flex; justify-content: space-between; align-items: center; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ddd; }
        .test-item.success { background: #f1f8e9; border-left-color: #4CAF50; }
        .test-item.error { background: #ffebee; border-left-color: #f44336; }
        .test-item.critical { border-left-width: 6px; }
        .status-badge { padding: 4px 12px; border-radius: 20px; color: white; font-size: 12px; font-weight: bold; }
        .status-success { background: #4CAF50; }
        .status-error { background: #f44336; }
        .response-time { color: #666; font-size: 12px; }
        .critical-badge { background: #ff5722; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; margin-left: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 楠楠家厨管理系统</h1>
            <h2>路由功能完整性测试报告</h2>
            <p>测试时间: 2025/5/29 14:29:11</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <h2>23</h2>
            </div>
            <div class="summary-card success">
                <h3>通过测试</h3>
                <h2>23</h2>
            </div>
            <div class="summary-card error">
                <h3>失败测试</h3>
                <h2>0</h2>
            </div>
            <div class="summary-card warning">
                <h3>关键问题</h3>
                <h2>0</h2>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 API端点测试结果</h3>
            
                <div class="test-item success critical">
                    <div>
                        <strong>菜单列表</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/menus</small>
                        <div class="response-time">响应时间: 849ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>今日菜单</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/menus/today</small>
                        <div class="response-time">响应时间: 847ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>菜品分类</strong>
                        
                        <br>
                        <small>/api/menus/categories</small>
                        <div class="response-time">响应时间: 565ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>菜单统计</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/menus/statistics</small>
                        <div class="response-time">响应时间: 1420ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>菜品列表</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/dishes</small>
                        <div class="response-time">响应时间: 572ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>订单列表</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/orders</small>
                        <div class="response-time">响应时间: 292ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>用户列表</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/api/users</small>
                        <div class="response-time">响应时间: 291ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>消息列表</strong>
                        
                        <br>
                        <small>/api/messages</small>
                        <div class="response-time">响应时间: 284ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>通知列表</strong>
                        
                        <br>
                        <small>/api/notifications</small>
                        <div class="response-time">响应时间: 287ms</div>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 通过
                        </span>
                        <div class="response-time">状态码: 200</div>
                    </div>
                </div>
            
        </div>

        <div class="test-section">
            <h3>🛣️ 路由可访问性测试结果</h3>
            
                <div class="test-item success critical">
                    <div>
                        <strong>仪表盘</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/dashboard</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>菜品管理</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/menu/dishes</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>分类管理</strong>
                        
                        <br>
                        <small>/menu/categories</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>今日菜单</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/menu/today</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>历史菜单</strong>
                        
                        <br>
                        <small>/menu/history</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>订单列表</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/order/list</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>今日订单</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/order/today</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>订单统计</strong>
                        
                        <br>
                        <small>/order/statistics</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success critical">
                    <div>
                        <strong>用户管理</strong>
                        <span class="critical-badge">关键</span>
                        <br>
                        <small>/user/list</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>家庭留言</strong>
                        
                        <br>
                        <small>/message/family</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>系统通知</strong>
                        
                        <br>
                        <small>/message/notifications</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>数据概览</strong>
                        
                        <br>
                        <small>/analytics/overview</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>菜品分析</strong>
                        
                        <br>
                        <small>/analytics/dishes</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
                <div class="test-item success ">
                    <div>
                        <strong>用户分析</strong>
                        
                        <br>
                        <small>/analytics/users</small>
                    </div>
                    <div>
                        <span class="status-badge status-success">
                            ✅ 可访问
                        </span>
                    </div>
                </div>
            
        </div>

        <div class="test-section">
            <h3>📋 测试总结</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <p><strong>测试完成情况:</strong></p>
                <ul>
                    <li>✅ 认证系统: 正常工作</li>
                    <li>✅ API端点: 9/9 个端点正常</li>
                    <li>✅ 路由系统: 14/14 个路由可访问</li>
                    <li>✅ 关键功能: 全部正常</li>
                </ul>
                
                <p style="color: #4CAF50; font-weight: bold;">🎉 所有关键功能测试通过，系统可以正常使用！</p>
            </div>
        </div>
    </div>
</body>
</html>
  