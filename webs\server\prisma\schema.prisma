// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  // relationMode = "prisma"
    // uncomment next line if you use Prisma <5.10
  // directUrl = env("DATABASE_URL_UNPOOLED")
}

model User {
  id            String         @id @default(cuid())
  name          String
  phone         String?        @unique
  password      String?
  avatar        String?
  openid        String?        @unique
  role          String         @default("user")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        Order[]
  messages      Message[]
  notifications Notification[]
}

model Dish {
  id          String     @id @default(cuid())
  name        String
  description String?
  image       String?
  categoryId  String
  category    Category   @relation(fields: [categoryId], references: [id])
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  menuItems   MenuItem[]

  @@index([categoryId])
}

model Category {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  dishes    Dish[]
}

model Menu {
  id        String     @id @default(cuid())
  date      DateTime
  isToday   Boolean    @default(false)
  remark    String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  items     MenuItem[]
}

model MenuItem {
  id        String   @id @default(cuid())
  menuId    String
  dishId    String
  count     Int
  menu      Menu     @relation(fields: [menuId], references: [id], onDelete: Cascade)
  dish      Dish     @relation(fields: [dishId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([menuId])
  @@index([dishId])
}

model Order {
  id         String    @id @default(cuid())
  userId     String
  user       User      @relation(fields: [userId], references: [id])
  items      Json      // 存储订单项的 JSON 数据
  remark     String?
  diningTime DateTime?
  status     String    @default("pending")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([userId])
}

model Message {
  id        String   @id @default(cuid())
  content   String
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model Notification {
  id        String   @id @default(cuid())
  content   String
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}
